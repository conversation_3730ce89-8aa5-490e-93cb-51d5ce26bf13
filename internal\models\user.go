package models

import (
	"time"
)

type User struct {
	ID           uint       `gorm:"primaryKey" json:"id"`
	LoginName    string     `gorm:"uniqueIndex" json:"login_name"`
	FullName     string     `json:"full_name"`
	Email        string     `gorm:"uniqueIndex" json:"email"`
	PasswordHash string     `json:"-"`
	Phone        string     `json:"phone"`
	Mobile       string     `json:"mobile"`
	Language     string     `json:"language" gorm:"default:magyar"`
	OrgID        string     `json:"org_id"`
	OrgName      string     `json:"org_name"`
	Roles        []Role     `gorm:"many2many:user_roles;" json:"roles"`
	CreatedAt    time.Time  `json:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at"`
	LastLogin    *time.Time `json:"last_login"`
}

type Role struct {
	ID          uint   `gorm:"primaryKey" json:"id"`
	Name        string `gorm:"uniqueIndex" json:"name"`
	DisplayName string `json:"display_name"`
	Users       []User `gorm:"many2many:user_roles;" json:"-"`
}

type CreateUserRequest struct {
	LoginName string `json:"login_name" binding:"required"`
	FullName  string `json:"full_name" binding:"required"`
	Email     string `json:"email" binding:"required,email"`
	Password  string `json:"password" binding:"required,min=6"`
	Phone     string `json:"phone"`
	Mobile    string `json:"mobile"`
	Language  string `json:"language"`
	OrgID     string `json:"org_id"`
	OrgName   string `json:"org_name"`
	RoleIDs   []uint `json:"role_ids"`
}

type UpdateUserRequest struct {
	FullName string `json:"full_name"`
	Email    string `json:"email"`
	Phone    string `json:"phone"`
	Mobile   string `json:"mobile"`
	Language string `json:"language"`
	OrgID    string `json:"org_id"`
	OrgName  string `json:"org_name"`
	RoleIDs  []uint `json:"role_ids"`
}

type LoginRequest struct {
	LoginName string `json:"login_name" binding:"required"`
	Password  string `json:"password" binding:"required"`
}

type LoginResponse struct {
	Token string `json:"token"`
	User  User   `json:"user"`
}