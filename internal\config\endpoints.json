{"database": {"driver": "sqlite", "path": "./data/app.db"}, "progress": {"exe": "C:/dlc83/bin/prowin32.exe", "ini": "g:/forras/honoris/progress/pfs/flotta.ini", "pf": "g:/forras/honoris/progress/pfs/flotta.pf", "workdir": "./temp"}, "sqlite_endpoints": {"auth/login": {"handler": "auth.<PERSON>", "methods": ["POST"], "auth_required": false}, "users": {"handler": "users.List", "methods": ["GET"], "auth_required": true, "roles": ["harum_admin", "rendszer<PERSON><PERSON>"]}}, "progress_endpoints": {"contracts": {"file": "contracts/list.p", "methods": ["GET"], "auth_required": true, "query_params": ["partner_id", "from_date", "to_date", "status"]}, "finance/invoices": {"file": "finance/invoices.p", "methods": ["GET"], "auth_required": true, "query_params": ["partner_id", "from_date", "to_date", "only_open"]}, "costs": {"file": "costs/list.p", "methods": ["GET"], "auth_required": true, "query_params": ["partner_id", "from_date", "to_date", "cost_type"]}, "services/closed": {"file": "services/closed.p", "methods": ["GET"], "auth_required": true, "query_params": ["partner_id", "from_date", "to_date"]}, "services/open": {"file": "services/open.p", "methods": ["GET"], "auth_required": true, "query_params": ["partner_id", "from_date", "to_date"]}, "fuel": {"file": "fuel/list.p", "methods": ["GET"], "auth_required": true, "query_params": ["partner_id", "from_date", "to_date", "type"]}, "mileage": {"file": "mileage/list.p", "methods": ["GET"], "auth_required": true, "query_params": ["partner_id", "from_date", "to_date"]}}}