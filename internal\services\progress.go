package services

import (
	"encoding/json"
	"fleet-info-portal/backend/internal/models"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"time"
)

type ProgressService struct {
	exePath string
	iniPath string
	pfPath  string
	workDir string
}

func NewProgressService() *ProgressService {
	return &ProgressService{
		exePath: os.Getenv("PROGRESS_EXE_PATH"),
		iniPath: os.<PERSON>env("PROGRESS_INI_PATH"),
		pfPath:  os.Getenv("PROGRESS_PF_PATH"),
		workDir: os.Getenv("PROGRESS_WORK_DIR"),
	}
}

// ExecuteProgressFile runs a Progress P file and returns the JSON result
func (s *ProgressService) ExecuteProgressFile(filename string, params map[string]string) (*models.ProgressResponse, error) {
	if s.exePath == "" || s.iniPath == "" || s.pfPath == "" {
		return nil, fmt.<PERSON><PERSON>rf("progress environment not configured")
	}

	// Build parameter string
	paramStr := ""
	for key, value := range params {
		if paramStr != "" {
			paramStr += " "
		}
		paramStr += fmt.Sprintf("%s=%s", key, value)
	}

	// Build command
	args := []string{
		"-ini", s.iniPath,
		"-pf", s.pfPath,
		"-p", filename,
	}
	if paramStr != "" {
		args = append(args, "-param", paramStr)
	}

	cmd := exec.Command(s.exePath, args...)
	if s.workDir != "" {
		cmd.Dir = s.workDir
	}

	// Execute command
	if err := cmd.Run(); err != nil {
		return nil, fmt.Errorf("progress execution failed: %v", err)
	}

	// Read output file
	outputFile := filepath.Join(os.TempDir(), "progress_output.json")
	data, err := os.ReadFile(outputFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read progress output: %v", err)
	}

	// Clean up output file
	os.Remove(outputFile)

	// Parse JSON response
	var response models.ProgressResponse
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, fmt.Errorf("failed to parse progress response: %v", err)
	}

	if !response.Success {
		return nil, fmt.Errorf("progress error: %s", response.Error)
	}

	return &response, nil
}

// Ping tests Progress connection
func (s *ProgressService) Ping() error {
	response, err := s.ExecuteProgressFile("api/ping.p", nil)
	if err != nil {
		return err
	}

	if !response.Success {
		return fmt.Errorf("ping failed: %s", response.Error)
	}

	return nil
}

// GetContracts retrieves contracts from Progress
func (s *ProgressService) GetContracts(query models.ContractQuery) ([]models.Contract, error) {
	params := make(map[string]string)
	if query.PartnerID != "" {
		params["partner_id"] = query.PartnerID
	}
	if query.FromDate != "" {
		params["from_date"] = query.FromDate
	}
	if query.ToDate != "" {
		params["to_date"] = query.ToDate
	}
	if query.Status != "" {
		params["status"] = query.Status
	}

	response, err := s.ExecuteProgressFile("contracts/list.p", params)
	if err != nil {
		return nil, err
	}

	// Parse contracts from response data
	var contracts []models.Contract
	if response.Data != nil {
		dataBytes, err := json.Marshal(response.Data)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal contracts data: %v", err)
		}
		
		if err := json.Unmarshal(dataBytes, &contracts); err != nil {
			return nil, fmt.Errorf("failed to parse contracts: %v", err)
		}
	}

	return contracts, nil
}

// GetInvoices retrieves invoices from Progress
func (s *ProgressService) GetInvoices(query models.InvoiceQuery) ([]models.Invoice, error) {
	params := make(map[string]string)
	if query.PartnerID != "" {
		params["partner_id"] = query.PartnerID
	}
	if query.FromDate != "" {
		params["from_date"] = query.FromDate
	}
	if query.ToDate != "" {
		params["to_date"] = query.ToDate
	}
	if query.OnlyOpen {
		params["only_open"] = "true"
	}

	response, err := s.ExecuteProgressFile("finance/invoices.p", params)
	if err != nil {
		return nil, err
	}

	var invoices []models.Invoice
	if response.Data != nil {
		dataBytes, err := json.Marshal(response.Data)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal invoices data: %v", err)
		}
		
		if err := json.Unmarshal(dataBytes, &invoices); err != nil {
			return nil, fmt.Errorf("failed to parse invoices: %v", err)
		}
	}

	return invoices, nil
}

// GetCosts retrieves costs from Progress
func (s *ProgressService) GetCosts(query models.CostQuery) ([]models.Cost, error) {
	params := make(map[string]string)
	if query.PartnerID != "" {
		params["partner_id"] = query.PartnerID
	}
	if query.FromDate != "" {
		params["from_date"] = query.FromDate
	}
	if query.ToDate != "" {
		params["to_date"] = query.ToDate
	}
	if query.CostType != "" {
		params["cost_type"] = query.CostType
	}

	response, err := s.ExecuteProgressFile("costs/list.p", params)
	if err != nil {
		return nil, err
	}

	var costs []models.Cost
	if response.Data != nil {
		dataBytes, err := json.Marshal(response.Data)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal costs data: %v", err)
		}
		
		if err := json.Unmarshal(dataBytes, &costs); err != nil {
			return nil, fmt.Errorf("failed to parse costs: %v", err)
		}
	}

	return costs, nil
}

// GetClosedServices retrieves closed service events from Progress
func (s *ProgressService) GetClosedServices(query models.ServiceQuery) ([]models.ServiceEvent, error) {
	params := make(map[string]string)
	if query.PartnerID != "" {
		params["partner_id"] = query.PartnerID
	}
	if query.FromDate != "" {
		params["from_date"] = query.FromDate
	}
	if query.ToDate != "" {
		params["to_date"] = query.ToDate
	}

	response, err := s.ExecuteProgressFile("services/closed.p", params)
	if err != nil {
		return nil, err
	}

	var services []models.ServiceEvent
	if response.Data != nil {
		dataBytes, err := json.Marshal(response.Data)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal services data: %v", err)
		}
		
		if err := json.Unmarshal(dataBytes, &services); err != nil {
			return nil, fmt.Errorf("failed to parse services: %v", err)
		}
	}

	return services, nil
}

// GetOpenServices retrieves open service events from Progress
func (s *ProgressService) GetOpenServices(query models.ServiceQuery) ([]models.ServiceEvent, error) {
	params := make(map[string]string)
	if query.PartnerID != "" {
		params["partner_id"] = query.PartnerID
	}
	if query.FromDate != "" {
		params["from_date"] = query.FromDate
	}
	if query.ToDate != "" {
		params["to_date"] = query.ToDate
	}

	response, err := s.ExecuteProgressFile("services/open.p", params)
	if err != nil {
		return nil, err
	}

	var services []models.ServiceEvent
	if response.Data != nil {
		dataBytes, err := json.Marshal(response.Data)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal services data: %v", err)
		}
		
		if err := json.Unmarshal(dataBytes, &services); err != nil {
			return nil, fmt.Errorf("failed to parse services: %v", err)
		}
	}

	return services, nil
}

// GetFuel retrieves fuel transactions from Progress
func (s *ProgressService) GetFuel(query models.FuelQuery) ([]models.FuelTransaction, error) {
	params := make(map[string]string)
	if query.PartnerID != "" {
		params["partner_id"] = query.PartnerID
	}
	if query.FromDate != "" {
		params["from_date"] = query.FromDate
	}
	if query.ToDate != "" {
		params["to_date"] = query.ToDate
	}
	if query.Type != "" {
		params["type"] = query.Type
	}

	response, err := s.ExecuteProgressFile("fuel/list.p", params)
	if err != nil {
		return nil, err
	}

	var fuel []models.FuelTransaction
	if response.Data != nil {
		dataBytes, err := json.Marshal(response.Data)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal fuel data: %v", err)
		}
		
		if err := json.Unmarshal(dataBytes, &fuel); err != nil {
			return nil, fmt.Errorf("failed to parse fuel: %v", err)
		}
	}

	return fuel, nil
}

// GetMileage retrieves mileage readings from Progress
func (s *ProgressService) GetMileage(query models.MileageQuery) ([]models.MileageReading, error) {
	params := make(map[string]string)
	if query.PartnerID != "" {
		params["partner_id"] = query.PartnerID
	}
	if query.FromDate != "" {
		params["from_date"] = query.FromDate
	}
	if query.ToDate != "" {
		params["to_date"] = query.ToDate
	}

	response, err := s.ExecuteProgressFile("mileage/list.p", params)
	if err != nil {
		return nil, err
	}

	var mileage []models.MileageReading
	if response.Data != nil {
		dataBytes, err := json.Marshal(response.Data)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal mileage data: %v", err)
		}
		
		if err := json.Unmarshal(dataBytes, &mileage); err != nil {
			return nil, fmt.Errorf("failed to parse mileage: %v", err)
		}
	}

	return mileage, nil
}