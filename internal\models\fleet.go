package models

import "time"

// Contract represents a contract from Progress system
type Contract struct {
	ContractNumber string `json:"contract_number"`
	LicensePlate   string `json:"license_plate"`
	Make           string `json:"make"`
	Model          string `json:"model"`
	UserName       string `json:"user_name"`
	ContractType   string `json:"contract_type"`
	StartDate      string `json:"start_date"`
	EndDate        string `json:"end_date"`
	Currency       string `json:"currency"`
	Device         string `json:"device"`
}

// Invoice represents an invoice from Progress system
type Invoice struct {
	InvoiceNumber     string  `json:"invoice_number"`
	InvoiceDate       string  `json:"invoice_date"`
	PerformanceDate   string  `json:"performance_date"`
	PaymentDeadline   string  `json:"payment_deadline"`
	Currency          string  `json:"currency"`
	NetAmount         float64 `json:"net_amount"`
	VatAmount         float64 `json:"vat_amount"`
	GrossAmount       float64 `json:"gross_amount"`
}

// Cost represents a cost entry from Progress system
type Cost struct {
	LicensePlate string  `json:"license_plate"`
	Date         string  `json:"date"`
	CostType     string  `json:"cost_type"`
	Currency     string  `json:"currency"`
	NetAmount    float64 `json:"net_amount"`
	VatAmount    float64 `json:"vat_amount"`
	GrossAmount  float64 `json:"gross_amount"`
}

// ServiceEvent represents a service event from Progress system
type ServiceEvent struct {
	LicensePlate      string  `json:"license_plate"`
	Contract          string  `json:"contract"`
	WorksheetNumber   string  `json:"worksheet_number"`
	ServiceDate       string  `json:"service_date"`
	WorkDescription   string  `json:"work_description"`
	NetTotal          *float64 `json:"net_total,omitempty"`
	ResponsiblePerson *string  `json:"responsible_person,omitempty"`
	ResponsiblePhone  *string  `json:"responsible_phone,omitempty"`
	Status            string   `json:"status"` // 'open' | 'closed'
	PickupDate        *string  `json:"pickup_date,omitempty"`
	ReturnDate        *string  `json:"return_date,omitempty"`
}

// FuelTransaction represents a fuel transaction from Progress system
type FuelTransaction struct {
	LicensePlate string   `json:"license_plate"`
	FuelType     string   `json:"fuel_type"`
	Datetime     string   `json:"datetime"`
	Location     string   `json:"location"`
	Quantity     *float64 `json:"quantity,omitempty"`
	Mileage      *int     `json:"mileage,omitempty"`
}

// MileageReading represents a mileage reading from Progress system
type MileageReading struct {
	LicensePlate string  `json:"license_plate"`
	Date         string  `json:"date"`
	Mileage      int     `json:"mileage"`
	ReadingType  string  `json:"reading_type"` // 'tankolás' | 'web_jelentés' | 'szervizesemény'
	ServiceCode  *string `json:"service_code,omitempty"`
}

// ProgressResponse represents the standard response from Progress P files
type ProgressResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

// Query parameters for Progress endpoints
type ContractQuery struct {
	PartnerID string `form:"partner_id"`
	FromDate  string `form:"from_date"`
	ToDate    string `form:"to_date"`
	Status    string `form:"status"`
}

type InvoiceQuery struct {
	PartnerID string `form:"partner_id"`
	FromDate  string `form:"from_date"`
	ToDate    string `form:"to_date"`
	OnlyOpen  bool   `form:"only_open"`
}

type CostQuery struct {
	PartnerID string `form:"partner_id"`
	FromDate  string `form:"from_date"`
	ToDate    string `form:"to_date"`
	CostType  string `form:"cost_type"`
}

type ServiceQuery struct {
	PartnerID string `form:"partner_id"`
	FromDate  string `form:"from_date"`
	ToDate    string `form:"to_date"`
}

type FuelQuery struct {
	PartnerID string `form:"partner_id"`
	FromDate  string `form:"from_date"`
	ToDate    string `form:"to_date"`
	Type      string `form:"type"`
}

type MileageQuery struct {
	PartnerID string `form:"partner_id"`
	FromDate  string `form:"from_date"`
	ToDate    string `form:"to_date"`
}