FROM golang:1.21-alpine AS builder

WORKDIR /app

# Install git and ca-certificates (needed for Go modules)
RUN apk add --no-cache git ca-certificates

# Copy go mod and sum files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=1 GOOS=linux go build -a -installsuffix cgo -o main cmd/server/main.go

# Final stage
FROM alpine:latest

# Install ca-certificates for HTTPS requests
RUN apk --no-cache add ca-certificates

WORKDIR /app

# Create directories
RUN mkdir -p /app/data /app/temp /app/progress

# Copy the binary from builder stage
COPY --from=builder /app/main .

# Copy Progress P files
COPY --from=builder /app/progress ./progress/

# Copy configuration files
COPY --from=builder /app/internal/config/endpoints.json ./internal/config/

# Expose port
EXPOSE 8080

# Run the application
CMD ["./main"]