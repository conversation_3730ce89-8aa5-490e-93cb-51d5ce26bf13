package handlers

import (
	"fleet-info-portal/backend/internal/models"
	"fleet-info-portal/backend/internal/utils"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type UserHandler struct {
	db *gorm.DB
}

func NewUserHandler(db *gorm.DB) *UserHandler {
	return &UserHandler{db: db}
}

// ListUsers returns all users (admin only)
func (h *UserHandler) ListUsers(c *gin.Context) {
	var users []models.User
	if err := h.db.Preload("Roles").Find(&users).Error; err != nil {
		utils.InternalError(c, "Failed to fetch users")
		return
	}

	utils.Success(c, users)
}

// CreateUser creates a new user (admin only)
func (h *UserHandler) CreateUser(c *gin.Context) {
	var req models.CreateUserRequest
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		utils.BadRequest(c, "Invalid request format")
		return
	}

	// Hash password
	hashedPassword, err := utils.HashPassword(req.Password)
	if err != nil {
		utils.InternalError(c, "Failed to hash password")
		return
	}

	// Find roles
	var roles []models.Role
	if len(req.RoleIDs) > 0 {
		if err := h.db.Where("id IN ?", req.RoleIDs).Find(&roles).Error; err != nil {
			utils.BadRequest(c, "Invalid role IDs")
			return
		}
	}

	// Create user
	user := models.User{
		LoginName:    req.LoginName,
		FullName:     req.FullName,
		Email:        req.Email,
		PasswordHash: hashedPassword,
		Phone:        req.Phone,
		Mobile:       req.Mobile,
		Language:     req.Language,
		OrgID:        req.OrgID,
		OrgName:      req.OrgName,
		Roles:        roles,
	}

	if user.Language == "" {
		user.Language = "magyar"
	}

	if err := h.db.Create(&user).Error; err != nil {
		utils.BadRequest(c, "Failed to create user")
		return
	}

	// Load user with roles
	h.db.Preload("Roles").First(&user, user.ID)

	utils.SuccessWithMessage(c, "User created successfully", user)
}

// UpdateUser updates an existing user (admin only)
func (h *UserHandler) UpdateUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "Invalid user ID")
		return
	}

	var req models.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request format")
		return
	}

	// Find user
	var user models.User
	if err := h.db.Preload("Roles").First(&user, uint(id)).Error; err != nil {
		utils.NotFound(c, "User not found")
		return
	}

	// Find roles if provided
	if len(req.RoleIDs) > 0 {
		var roles []models.Role
		if err := h.db.Where("id IN ?", req.RoleIDs).Find(&roles).Error; err != nil {
			utils.BadRequest(c, "Invalid role IDs")
			return
		}
		user.Roles = roles
	}

	// Update user fields
	if req.FullName != "" {
		user.FullName = req.FullName
	}
	if req.Email != "" {
		user.Email = req.Email
	}
	if req.Phone != "" {
		user.Phone = req.Phone
	}
	if req.Mobile != "" {
		user.Mobile = req.Mobile
	}
	if req.Language != "" {
		user.Language = req.Language
	}
	if req.OrgID != "" {
		user.OrgID = req.OrgID
	}
	if req.OrgName != "" {
		user.OrgName = req.OrgName
	}

	// Save user
	if err := h.db.Save(&user).Error; err != nil {
		utils.InternalError(c, "Failed to update user")
		return
	}

	utils.SuccessWithMessage(c, "User updated successfully", user)
}

// DeleteUser deletes a user (admin only)
func (h *UserHandler) DeleteUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "Invalid user ID")
		return
	}

	// Check if user exists
	var user models.User
	if err := h.db.First(&user, uint(id)).Error; err != nil {
		utils.NotFound(c, "User not found")
		return
	}

	// Delete user (this will also remove role associations)
	if err := h.db.Delete(&user).Error; err != nil {
		utils.InternalError(c, "Failed to delete user")
		return
	}

	utils.SuccessWithMessage(c, "User deleted successfully", nil)
}