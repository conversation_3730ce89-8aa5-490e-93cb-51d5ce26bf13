package handlers

import (
	"fleet-info-portal/backend/internal/models"
	"fleet-info-portal/backend/internal/utils"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type AuthHandler struct {
	db *gorm.DB
}

func NewAuthHandler(db *gorm.DB) *AuthHandler {
	return &AuthHandler{db: db}
}

// Login handles user authentication
func (h *AuthHandler) Login(c *gin.Context) {
	var req models.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request format")
		return
	}

	// Find user by login name
	var user models.User
	if err := h.db.Preload("Roles").Where("login_name = ?", req.LoginName).First(&user).Error; err != nil {
		utils.Unauthorized(c, "Invalid credentials")
		return
	}

	// Check password
	if !utils.CheckPasswordHash(req.Password, user.PasswordHash) {
		utils.Unauthorized(c, "Invalid credentials")
		return
	}

	// Generate JWT token
	token, err := utils.GenerateToken(&user)
	if err != nil {
		utils.InternalError(c, "Failed to generate token")
		return
	}

	// Update last login
	now := time.Now()
	user.LastLogin = &now
	h.db.Save(&user)

	response := models.LoginResponse{
		Token: token,
		User:  user,
	}

	utils.SuccessWithMessage(c, "Login successful", response)
}

// Me returns current user info
func (h *AuthHandler) Me(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not found in context")
		return
	}

	var user models.User
	if err := h.db.Preload("Roles").First(&user, userID).Error; err != nil {
		utils.NotFound(c, "User not found")
		return
	}

	utils.Success(c, user)
}

// Logout handles user logout (client-side token removal)
func (h *AuthHandler) Logout(c *gin.Context) {
	utils.SuccessWithMessage(c, "Logout successful", nil)
}