package main

import (
	"fleet-info-portal/backend/internal/config"
	"fleet-info-portal/backend/internal/database"
	"fleet-info-portal/backend/internal/handlers"
	"fleet-info-portal/backend/internal/middleware"
	"log"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using system environment variables")
	}

	// Initialize database
	db, err := database.InitDB()
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// Initialize Gin router
	if os.Getenv("GO_ENV") == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	r := gin.Default()

	// Add middleware
	r.Use(middleware.CORS())
	r.Use(middleware.Logger())

	// Health check endpoint
	r.GET("/api/health", func(c *gin.Context) {
		c.<PERSON>(200, gin.H{"status": "ok", "service": "fleet-info-portal"})
	})

	// Initialize handlers with database
	authHandler := handlers.NewAuthHandler(db)
	userHandler := handlers.NewUserHandler(db)
	fleetHandler := handlers.NewFleetHandler()

	// Public routes
	r.POST("/api/auth/login", authHandler.Login)

	// Protected routes
	protected := r.Group("/api")
	protected.Use(middleware.AuthRequired())
	{
		protected.GET("/auth/me", authHandler.Me)
		protected.POST("/auth/logout", authHandler.Logout)

		// Admin only routes
		admin := protected.Group("/admin")
		admin.Use(middleware.RoleRequired("harum_admin", "rendszeradmin"))
		{
			admin.GET("/users", userHandler.ListUsers)
			admin.POST("/users", userHandler.CreateUser)
			admin.PUT("/users/:id", userHandler.UpdateUser)
			admin.DELETE("/users/:id", userHandler.DeleteUser)
		}

		// Business data routes (Progress integration)
		protected.GET("/contracts", fleetHandler.GetContracts)
		protected.GET("/finance/invoices", fleetHandler.GetInvoices)
		protected.GET("/costs", fleetHandler.GetCosts)
		protected.GET("/services/closed", fleetHandler.GetClosedServices)
		protected.GET("/services/open", fleetHandler.GetOpenServices)
		protected.GET("/fuel", fleetHandler.GetFuel)
		protected.GET("/mileage", fleetHandler.GetMileage)
	}

	port := os.Getenv("API_PORT")
	if port == "" {
		port = "8080"
	}

	log.Printf("Server starting on port %s", port)
	r.Run(":" + port)
}