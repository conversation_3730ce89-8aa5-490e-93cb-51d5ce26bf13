package middleware

import (
	"fleet-info-portal/backend/internal/utils"
	"strings"

	"github.com/gin-gonic/gin"
)

// AuthRequired middleware validates JWT token
func AuthRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.<PERSON>("Authorization")
		if authHeader == "" {
			utils.Unauthorized(c, "Authorization header required")
			c.Abort()
			return
		}

		// Check if header starts with "Bearer "
		if !strings.HasPrefix(authHeader, "Bearer ") {
			utils.Unauthorized(c, "Authorization header must start with Bear<PERSON>")
			c.Abort()
			return
		}

		// Extract token
		token := strings.TrimPrefix(authHeader, "Bearer ")
		if token == "" {
			utils.Unauthorized(c, "Token required")
			c.Abort()
			return
		}

		// Validate token
		claims, err := utils.ValidateToken(token)
		if err != nil {
			utils.Unauthorized(c, "Invalid token")
			c.Abort()
			return
		}

		// Store user info in context
		c.Set("user_id", claims.UserID)
		c.Set("user_login", claims.LoginName)
		c.Set("user_roles", claims.Roles)
		c.Set("user_org_id", claims.OrgID)

		c.Next()
	}
}

// RoleRequired middleware checks if user has required roles
func RoleRequired(requiredRoles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userRoles, exists := c.Get("user_roles")
		if !exists {
			utils.Forbidden(c, "User roles not found")
			c.Abort()
			return
		}

		roles, ok := userRoles.([]string)
		if !ok {
			utils.Forbidden(c, "Invalid user roles")
			c.Abort()
			return
		}

		if !utils.HasRole(roles, requiredRoles) {
			utils.Forbidden(c, "Insufficient permissions")
			c.Abort()
			return
		}

		c.Next()
	}
}