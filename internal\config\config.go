package config

import (
	"encoding/json"
	"os"
	"strconv"
)

type Config struct {
	Environment    string
	Port           string
	FrontendURL    string
	DBPath         string
	JWTSecret      string
	JWTExpiryHours int
	ProgressConfig ProgressConfig
	LogLevel       string
}

type ProgressConfig struct {
	ExePath string
	IniPath string
	PfPath  string
	WorkDir string
}

type EndpointConfig struct {
	Database          DatabaseConfig            `json:"database"`
	Progress          ProgressConfig            `json:"progress"`
	SQLiteEndpoints   map[string]EndpointDetail `json:"sqlite_endpoints"`
	ProgressEndpoints map[string]EndpointDetail `json:"progress_endpoints"`
}

type DatabaseConfig struct {
	Driver string `json:"driver"`
	Path   string `json:"path"`
}

type EndpointDetail struct {
	Handler      string   `json:"handler,omitempty"`
	File         string   `json:"file,omitempty"`
	Methods      []string `json:"methods"`
	AuthRequired bool     `json:"auth_required"`
	Roles        []string `json:"roles,omitempty"`
	QueryParams  []string `json:"query_params,omitempty"`
}

func Load() *Config {
	jwtExpiryHours := 24
	if hours := os.Getenv("JWT_EXPIRY_HOURS"); hours != "" {
		if parsed, err := strconv.Atoi(hours); err == nil {
			jwtExpiryHours = parsed
		}
	}

	return &Config{
		Environment: getEnv("GO_ENV", "development"),
		Port:        getEnv("API_PORT", "8080"),
		FrontendURL: getEnv("FRONTEND_URL", "http://localhost:3000"),
		DBPath:      getEnv("DB_PATH", "./data/app.db"),
		JWTSecret:   getEnv("JWT_SECRET", "dev-secret-change-in-production"),
		JWTExpiryHours: jwtExpiryHours,
		ProgressConfig: ProgressConfig{
			ExePath: getEnv("PROGRESS_EXE_PATH", "C:/dlc83/bin/prowin32.exe"),
			IniPath: getEnv("PROGRESS_INI_PATH", "g:/forras/honoris/progress/pfs/flotta.ini"),
			PfPath:  getEnv("PROGRESS_PF_PATH", "g:/forras/honoris/progress/pfs/flotta.pf"),
			WorkDir: getEnv("PROGRESS_WORK_DIR", "./temp"),
		},
		LogLevel: getEnv("LOG_LEVEL", "info"),
	}
}

func LoadEndpointConfig() (*EndpointConfig, error) {
	data, err := os.ReadFile("internal/config/endpoints.json")
	if err != nil {
		return nil, err
	}

	var config EndpointConfig
	err = json.Unmarshal(data, &config)
	if err != nil {
		return nil, err
	}

	return &config, nil
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}