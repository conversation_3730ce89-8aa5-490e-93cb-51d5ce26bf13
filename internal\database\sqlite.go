package database

import (
	"fleet-info-portal/backend/internal/models"
	"log"
	"os"
	"path/filepath"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func InitDB() (*gorm.DB, error) {
	dbPath := os.Getenv("DB_PATH")
	if dbPath == "" {
		dbPath = "./data/app.db"
	}

	// Ensure directory exists
	dir := filepath.Dir(dbPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, err
	}

	// Configure GORM logger
	gormLogger := logger.Default
	if os.Getenv("GO_ENV") == "development" {
		gormLogger = logger.Default.LogMode(logger.Info)
	}

	db, err := gorm.Open(sqlite.Open(dbPath), &gorm.Config{
		Logger: gormLogger,
	})
	if err != nil {
		return nil, err
	}

	// Run migrations
	if err := migrate(db); err != nil {
		return nil, err
	}

	// Seed default data
	if err := seedData(db); err != nil {
		return nil, err
	}

	return db, nil
}

func migrate(db *gorm.DB) error {
	return db.AutoMigrate(
		&models.User{},
		&models.Role{},
	)
}

func seedData(db *gorm.DB) error {
	// Check if roles already exist
	var roleCount int64
	db.Model(&models.Role{}).Count(&roleCount)
	if roleCount > 0 {
		return nil // Already seeded
	}

	// Create default roles
	roles := []models.Role{
		{
			Name:        "felhasznalo",
			DisplayName: "Felhasználó",
		},
		{
			Name:        "harum_admin",
			DisplayName: "Harum Admin",
		},
		{
			Name:        "rendszeradmin",
			DisplayName: "Rendszer Admin",
		},
	}

	for _, role := range roles {
		if err := db.Create(&role).Error; err != nil {
			return err
		}
	}

	// Create default admin user
	adminRole := models.Role{}
	if err := db.Where("name = ?", "rendszeradmin").First(&adminRole).Error; err != nil {
		return err
	}

	// Check if admin user already exists
	var userCount int64
	db.Model(&models.User{}).Count(&userCount)
	if userCount > 0 {
		return nil // Users already exist
	}

	// Hash password (we'll implement this in utils/hash.go)
	hashedPassword := "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi" // "password"

	adminUser := models.User{
		LoginName:    "admin",
		FullName:     "System Administrator",
		Email:        "<EMAIL>",
		PasswordHash: hashedPassword,
		Language:     "magyar",
		OrgID:        "HARUM",
		OrgName:      "Harum Kft.",
		Roles:        []models.Role{adminRole},
	}

	if err := db.Create(&adminUser).Error; err != nil {
		return err
	}

	log.Println("Database seeded with default data")
	log.Println("Default admin user: admin / password")

	return nil
}