package handlers

import (
	"fleet-info-portal/backend/internal/models"
	"fleet-info-portal/backend/internal/services"
	"fleet-info-portal/backend/internal/utils"

	"github.com/gin-gonic/gin"
)

type FleetHandler struct {
	progressService *services.ProgressService
}

func NewFleetHandler() *FleetHandler {
	return &FleetHandler{
		progressService: services.NewProgressService(),
	}
}

// GetContracts retrieves contracts from Progress
func (h *FleetHandler) GetContracts(c *gin.Context) {
	var query models.ContractQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		utils.BadRequest(c, "Invalid query parameters")
		return
	}

	contracts, err := h.progressService.GetContracts(query)
	if err != nil {
		utils.InternalError(c, "Failed to fetch contracts: "+err.Error())
		return
	}

	utils.Success(c, contracts)
}

// GetInvoices retrieves invoices from Progress
func (h *FleetHandler) GetInvoices(c *gin.Context) {
	var query models.InvoiceQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		utils.BadRequest(c, "Invalid query parameters")
		return
	}

	invoices, err := h.progressService.GetInvoices(query)
	if err != nil {
		utils.InternalError(c, "Failed to fetch invoices: "+err.Error())
		return
	}

	utils.Success(c, invoices)
}

// GetCosts retrieves costs from Progress
func (h *FleetHandler) GetCosts(c *gin.Context) {
	var query models.CostQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		utils.BadRequest(c, "Invalid query parameters")
		return
	}

	costs, err := h.progressService.GetCosts(query)
	if err != nil {
		utils.InternalError(c, "Failed to fetch costs: "+err.Error())
		return
	}

	utils.Success(c, costs)
}

// GetClosedServices retrieves closed service events from Progress
func (h *FleetHandler) GetClosedServices(c *gin.Context) {
	var query models.ServiceQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		utils.BadRequest(c, "Invalid query parameters")
		return
	}

	services, err := h.progressService.GetClosedServices(query)
	if err != nil {
		utils.InternalError(c, "Failed to fetch closed services: "+err.Error())
		return
	}

	utils.Success(c, services)
}

// GetOpenServices retrieves open service events from Progress
func (h *FleetHandler) GetOpenServices(c *gin.Context) {
	var query models.ServiceQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		utils.BadRequest(c, "Invalid query parameters")
		return
	}

	services, err := h.progressService.GetOpenServices(query)
	if err != nil {
		utils.InternalError(c, "Failed to fetch open services: "+err.Error())
		return
	}

	utils.Success(c, services)
}

// GetFuel retrieves fuel transactions from Progress
func (h *FleetHandler) GetFuel(c *gin.Context) {
	var query models.FuelQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		utils.BadRequest(c, "Invalid query parameters")
		return
	}

	fuel, err := h.progressService.GetFuel(query)
	if err != nil {
		utils.InternalError(c, "Failed to fetch fuel data: "+err.Error())
		return
	}

	utils.Success(c, fuel)
}

// GetMileage retrieves mileage readings from Progress
func (h *FleetHandler) GetMileage(c *gin.Context) {
	var query models.MileageQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		utils.BadRequest(c, "Invalid query parameters")
		return
	}

	mileage, err := h.progressService.GetMileage(query)
	if err != nil {
		utils.InternalError(c, "Failed to fetch mileage data: "+err.Error())
		return
	}

	utils.Success(c, mileage)
}